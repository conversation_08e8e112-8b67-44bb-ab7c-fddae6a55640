# Data Configuration for Persian Text Embedding Distillation

# Dataset Configuration
dataset:
  name: "persian_text_pairs"
  data_dir: "data/"
  
  # Data files
  train_file: "train.jsonl"
  validation_file: "validation.jsonl"
  test_file: "test.jsonl"
  
  # Data format: each line should be a JSON object with:
  # {"text1": "first text", "text2": "second text", "label": 1/0}
  # or {"anchor": "anchor text", "positive": "positive text", "negative": "negative text"}
  
  # Data loading
  streaming: false
  cache_dir: "data/cache"
  preprocessing_num_workers: 8
  
  # Data splits
  train_split_ratio: 0.8
  validation_split_ratio: 0.1
  test_split_ratio: 0.1
  
  # Sampling
  max_samples_train: null  # null for all samples
  max_samples_eval: 10000
  
# Text Preprocessing
preprocessing:
  # Persian-specific preprocessing
  normalize_persian: true
  remove_diacritics: true
  normalize_whitespace: true
  remove_extra_spaces: true
  
  # Tokenization
  max_length: 512
  truncation: true
  padding: "max_length"  # max_length, longest, do_not_pad
  return_attention_mask: true
  return_token_type_ids: false
  
  # Text cleaning
  remove_urls: true
  remove_emails: true
  remove_phone_numbers: true
  remove_special_chars: false
  lowercase: false  # Keep Persian case sensitivity
  
  # Language filtering
  filter_non_persian: true
  min_persian_ratio: 0.7  # Minimum ratio of Persian characters
  
# Data Augmentation
augmentation:
  enabled: true
  
  # Text augmentation techniques
  synonym_replacement:
    enabled: true
    probability: 0.1
    max_replacements: 3
  
  random_insertion:
    enabled: true
    probability: 0.05
    max_insertions: 2
  
  random_deletion:
    enabled: true
    probability: 0.05
    max_deletions: 2
  
  back_translation:
    enabled: false  # Requires additional models
    source_lang: "fa"
    intermediate_lang: "en"
  
  # Contrastive augmentation
  hard_negative_mining:
    enabled: true
    similarity_threshold: 0.7
    max_hard_negatives: 5
  
  # Paraphrasing (if available)
  paraphrasing:
    enabled: false
    model_name: "persian_paraphraser"
    probability: 0.1

# Contrastive Learning Data
contrastive:
  # Pair generation
  positive_pair_strategies:
    - "semantic_similarity"
    - "paraphrase"
    - "translation_pairs"
  
  negative_pair_strategies:
    - "random_sampling"
    - "hard_negative_mining"
    - "topic_mismatch"
  
  # Triplet configuration
  use_triplets: true
  triplet_margin: 0.2
  
  # Batch composition
  positive_ratio: 0.5  # Ratio of positive pairs in batch
  negative_ratio: 0.5  # Ratio of negative pairs in batch
  
  # In-batch negatives
  use_in_batch_negatives: true
  temperature: 0.05

# Persian Language Specific
persian_config:
  # Character normalization
  normalize_arabic_chars: true
  normalize_persian_chars: true
  
  # Word normalization
  normalize_punctuation: true
  normalize_numbers: true
  
  # Tokenizer settings
  tokenizer_type: "wordpiece"  # wordpiece, sentencepiece, bpe
  vocab_size: 50000
  
  # Special tokens
  special_tokens:
    pad_token: "[PAD]"
    unk_token: "[UNK]"
    cls_token: "[CLS]"
    sep_token: "[SEP]"
    mask_token: "[MASK]"

# Validation and Quality Control
quality_control:
  # Text quality filters
  min_text_length: 10
  max_text_length: 1000
  min_word_count: 3
  max_word_count: 200
  
  # Language detection
  use_language_detection: true
  min_confidence: 0.8
  
  # Duplicate detection
  remove_duplicates: true
  similarity_threshold: 0.95
  
  # Content filtering
  filter_inappropriate_content: true
  
# Caching and Performance
caching:
  cache_preprocessed_data: true
  cache_tokenized_data: true
  cache_embeddings: false  # Cache teacher embeddings
  
  # Memory management
  max_cache_size: "10GB"
  cleanup_cache_on_exit: false
