# Training Configuration for Persian Text Embedding Distillation

# Training Parameters
training:
  num_epochs: 10
  learning_rate: 5e-5
  weight_decay: 0.01
  warmup_steps: 1000
  max_grad_norm: 1.0
  
  # Batch sizes
  per_device_train_batch_size: 8
  per_device_eval_batch_size: 16
  gradient_accumulation_steps: 4
  dataloader_num_workers: 4
  dataloader_pin_memory: true
  
  # Optimization
  optimizer: "adamw"  # adamw, adam, sgd
  scheduler: "cosine"  # linear, cosine, polynomial
  adam_beta1: 0.9
  adam_beta2: 0.999
  adam_epsilon: 1e-8
  
  # Mixed Precision
  fp16: true
  bf16: false  # Use bf16 if available (A100, H100)
  fp16_opt_level: "O1"
  
  # Gradient Checkpointing
  gradient_checkpointing: true
  
# Distributed Training
distributed:
  backend: "nccl"  # nccl, gloo
  init_method: "env://"
  world_size: 4  # Number of GPUs
  find_unused_parameters: false
  
  # DeepSpeed Configuration
  use_deepspeed: false
  deepspeed_config: "configs/deepspeed_config.json"
  
  # Memory Optimization
  cpu_offload: false
  pin_memory: true
  
# Data Loading
data_loading:
  shuffle_train: true
  shuffle_eval: false
  drop_last_train: true
  drop_last_eval: false
  prefetch_factor: 2
  persistent_workers: true

# Evaluation
evaluation:
  evaluation_strategy: "steps"  # steps, epoch, no
  eval_steps: 500
  eval_delay: 0
  eval_accumulation_steps: 1
  
  # Metrics
  compute_metrics: true
  metric_names:
    - "contrastive_accuracy"
    - "embedding_similarity"
    - "retrieval_accuracy"
    - "spearman_correlation"

# Logging and Monitoring
logging:
  logging_strategy: "steps"
  logging_steps: 100
  log_level: "info"
  
  # Weights & Biases
  use_wandb: true
  wandb_project: "persian-embedding-distillation"
  wandb_entity: null  # Your W&B username/team
  wandb_run_name: null  # Auto-generated if null
  
  # TensorBoard
  use_tensorboard: true
  tensorboard_log_dir: "logs/tensorboard"
  
  # Console Logging
  disable_tqdm: false
  log_predictions: false

# Early Stopping
early_stopping:
  enabled: true
  patience: 3
  min_delta: 0.001
  restore_best_weights: true

# Reproducibility
seed: 42
deterministic: true

# Resource Management
resource_management:
  max_memory_per_gpu: "20GB"
  empty_cache_steps: 100
  monitor_memory: true
  
# Resuming Training
resume:
  resume_from_checkpoint: null  # Path to checkpoint directory
  ignore_data_skip: false
