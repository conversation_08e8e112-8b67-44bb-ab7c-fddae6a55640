# Model Configuration for Persian Text Embedding Distillation

teacher_model:
  name: "<PERSON><PERSON><PERSON><PERSON>"  # or "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2"
  model_path: "HooshvareLab/bert-fa-zwnj-base"  # TookaBERT path
  embedding_dim: 768
  max_length: 512
  pooling_strategy: "mean"  # mean, cls, max
  normalize_embeddings: true
  use_vllm: true
  vllm_config:
    tensor_parallel_size: 2
    max_model_len: 512
    gpu_memory_utilization: 0.8
    dtype: "float16"

student_model:
  name: "Command-R-7B"
  model_path: "CohereForAI/c4ai-command-r-v01"  # or local path
  embedding_dim: 4096  # Command-R hidden size
  projection_dim: 768   # Project to match teacher
  max_length: 2048
  use_gradient_checkpointing: true
  freeze_base_model: false  # Set to true for parameter-efficient training
  lora_config:
    enabled: false  # Enable for LoRA fine-tuning
    r: 16
    alpha: 32
    dropout: 0.1
    target_modules: ["q_proj", "v_proj", "k_proj", "o_proj"]
  
  # Embedding extraction configuration
  embedding_layer: "last_hidden_state"  # last_hidden_state, pooler_output
  pooling_strategy: "mean"  # mean, cls, max, attention_weighted
  add_projection_head: true
  projection_layers: 2
  projection_dropout: 0.1
  activation: "gelu"

distillation_model:
  temperature: 0.05  # Temperature for contrastive loss
  similarity_function: "cosine"  # cosine, dot_product, euclidean
  margin: 0.2  # Margin for triplet loss
  
  # Loss weights
  contrastive_weight: 1.0
  mse_weight: 0.1  # MSE between teacher and student embeddings
  cosine_weight: 0.1  # Cosine similarity loss
  
  # Hard negative mining
  hard_negative_mining: true
  negative_sampling_ratio: 4  # Number of negatives per positive
  
  # Auxiliary objectives
  use_auxiliary_loss: true
  auxiliary_weight: 0.05
  
# Model loading and saving
model_loading:
  load_in_8bit: false
  load_in_4bit: false
  torch_dtype: "float16"  # float16, bfloat16, float32
  device_map: "auto"
  trust_remote_code: true

checkpointing:
  save_strategy: "steps"  # steps, epoch
  save_steps: 1000
  save_total_limit: 3
  load_best_model_at_end: true
  metric_for_best_model: "eval_contrastive_accuracy"
  greater_is_better: true
