# Core ML Libraries
torch>=2.0.0
torchvision>=0.15.0
transformers>=4.30.0
tokenizers>=0.13.0
datasets>=2.12.0
accelerate>=0.20.0

# VLLM for efficient inference
vllm>=0.2.0

# Persian Language Processing
hazm>=0.7.0
parsivar>=0.2.0

# Distributed Training
deepspeed>=0.9.0

# Data Processing
numpy>=1.21.0
pandas>=1.5.0
scikit-learn>=1.2.0
scipy>=1.9.0

# Visualization and Monitoring
matplotlib>=3.5.0
seaborn>=0.11.0
tensorboard>=2.12.0
wandb>=0.15.0

# Configuration Management
pyyaml>=6.0
omegaconf>=2.3.0
hydra-core>=1.3.0

# Utilities
tqdm>=4.64.0
rich>=13.0.0
click>=8.1.0
loguru>=0.7.0

# Evaluation Metrics
sentence-transformers>=2.2.0
faiss-cpu>=1.7.0
faiss-gpu>=1.7.0

# Memory Optimization
psutil>=5.9.0
py3nvml>=0.2.7

# Development Tools
pytest>=7.0.0
black>=23.0.0
isort>=5.12.0
flake8>=6.0.0
mypy>=1.0.0

# Optional: For specific Persian models
# persian-transformers>=0.1.0
