# Persian Text Embedding Distillation

A comprehensive knowledge distillation framework for creating high-quality Persian text embeddings using Command-R 7B as a student model and pre-trained Persian embedding models as teachers.

## Overview

This project implements knowledge distillation to transfer embedding knowledge from a teacher model (e.g., TookaBERT) to a student decoder model (Command-R 7B) using contrastive learning objectives.

## Features

- **Multi-GPU Distributed Training**: Efficient training across multiple GPUs using PyTorch DDP
- **VLLM Integration**: Optimized teacher model inference for faster distillation
- **Persian Language Support**: Specialized preprocessing and tokenization for Persian text
- **Contrastive Learning**: Advanced contrastive loss with temperature scaling and hard negative mining
- **Memory Optimization**: Gradient checkpointing and mixed precision training
- **Comprehensive Evaluation**: Multiple metrics for embedding quality assessment

## Project Structure

```
distillation/
├── configs/                 # Configuration files
│   ├── model_configs.yaml
│   ├── training_configs.yaml
│   └── data_configs.yaml
├── src/
│   ├── models/             # Model implementations
│   │   ├── teacher_model.py
│   │   ├── student_model.py
│   │   └── distillation_model.py
│   ├── data/               # Data processing
│   │   ├── dataset.py
│   │   ├── preprocessing.py
│   │   └── collator.py
│   ├── training/           # Training components
│   │   ├── trainer.py
│   │   ├── losses.py
│   │   └── distributed.py
│   ├── evaluation/         # Evaluation metrics
│   │   ├── metrics.py
│   │   └── benchmarks.py
│   └── utils/              # Utilities
│       ├── logging.py
│       ├── config.py
│       └── memory.py
├── scripts/                # Training and evaluation scripts
│   ├── train.py
│   ├── evaluate.py
│   └── inference.py
├── data/                   # Data directory
├── checkpoints/            # Model checkpoints
├── logs/                   # Training logs
└── requirements.txt
```

## Quick Start

1. **Install Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Prepare Data**:
   ```bash
   python scripts/prepare_data.py --config configs/data_configs.yaml
   ```

3. **Start Training**:
   ```bash
   torchrun --nproc_per_node=4 scripts/train.py --config configs/training_configs.yaml
   ```

4. **Evaluate Model**:
   ```bash
   python scripts/evaluate.py --checkpoint checkpoints/best_model.pt
   ```

## Configuration

All hyperparameters and model settings are managed through YAML configuration files in the `configs/` directory. Key configurations include:

- **Model Settings**: Teacher/student model parameters, embedding dimensions
- **Training Parameters**: Learning rates, batch sizes, optimization settings
- **Data Processing**: Tokenization, augmentation, and sampling strategies
- **Distributed Training**: Multi-GPU settings and synchronization parameters

## Requirements

- Python 3.8+
- PyTorch 2.0+
- Transformers 4.30+
- VLLM 0.2+
- Persian text processing libraries

## License

MIT License
