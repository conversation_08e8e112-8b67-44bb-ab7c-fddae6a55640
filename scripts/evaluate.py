#!/usr/bin/env python3
"""
Evaluation script for Persian text embedding distillation.
"""

import os
import sys
import argparse
import logging
import torch
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent.parent / "src"))

from utils.config import Config<PERSON>anager
from models.distillation_model import load_pretrained_student
from evaluation.benchmarks import BenchmarkSuite
from evaluation.metrics import ComprehensiveEvaluator
from utils.logging import setup_logging

logger = logging.getLogger(__name__)


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Evaluate Persian text embedding model")
    
    # Model and configuration
    parser.add_argument("--model-path", type=str, required=True,
                       help="Path to trained model checkpoint")
    parser.add_argument("--model-config", type=str, required=True,
                       help="Path to model configuration file")
    parser.add_argument("--eval-config", type=str, required=True,
                       help="Path to evaluation configuration file")
    
    # Output options
    parser.add_argument("--output-dir", type=str, default="evaluation_results",
                       help="Output directory for evaluation results")
    parser.add_argument("--save-embeddings", action="store_true",
                       help="Save computed embeddings")
    
    # Evaluation options
    parser.add_argument("--batch-size", type=int, default=32,
                       help="Batch size for evaluation")
    parser.add_argument("--max-samples", type=int, default=None,
                       help="Maximum number of samples to evaluate")
    
    # Device options
    parser.add_argument("--device", type=str, default="auto",
                       help="Device to use (auto, cpu, cuda)")
    parser.add_argument("--fp16", action="store_true",
                       help="Use half precision")
    
    # Debug options
    parser.add_argument("--debug", action="store_true",
                       help="Enable debug mode")
    parser.add_argument("--verbose", action="store_true",
                       help="Verbose output")
    
    return parser.parse_args()


def setup_device(device_arg: str) -> torch.device:
    """Setup computation device."""
    if device_arg == "auto":
        if torch.cuda.is_available():
            device = torch.device("cuda")
            logger.info(f"Using CUDA device: {torch.cuda.get_device_name()}")
        else:
            device = torch.device("cpu")
            logger.info("Using CPU device")
    else:
        device = torch.device(device_arg)
        logger.info(f"Using specified device: {device}")
    
    return device


def load_model(model_path: str, model_config: dict, device: torch.device):
    """Load trained model."""
    logger.info(f"Loading model from {model_path}")
    
    # Load student model
    student_model = load_pretrained_student(model_path, model_config)
    student_model = student_model.to(device)
    student_model.eval()
    
    logger.info(f"Loaded model with {student_model.get_num_parameters()} parameters")
    return student_model


def run_benchmarks(model, eval_config: dict, device: torch.device, 
                  output_dir: Path, args) -> list:
    """Run benchmark evaluations."""
    logger.info("Starting benchmark evaluations")
    
    # Initialize benchmark suite
    benchmark_suite = BenchmarkSuite(eval_config)
    
    # Run all benchmarks
    results = benchmark_suite.evaluate_all(model, device)
    
    # Save results
    results_file = output_dir / "benchmark_results.json"
    benchmark_suite.save_results(results, str(results_file))
    
    # Print summary
    if args.verbose:
        benchmark_suite.print_summary(results)
    
    logger.info(f"Completed {len(results)} benchmarks")
    return results


def run_custom_evaluation(model, eval_config: dict, device: torch.device,
                         output_dir: Path, args) -> dict:
    """Run custom evaluation tasks."""
    logger.info("Starting custom evaluations")
    
    custom_metrics = {}
    
    # Example: Evaluate on custom text pairs
    if "custom_similarity" in eval_config:
        logger.info("Running custom similarity evaluation")
        
        # Load custom data
        custom_data = eval_config["custom_similarity"]
        texts1 = custom_data.get("texts1", [])
        texts2 = custom_data.get("texts2", [])
        
        if texts1 and texts2:
            # Encode texts
            with torch.no_grad():
                embeddings1 = model.encode(texts1, batch_size=args.batch_size)
                embeddings2 = model.encode(texts2, batch_size=args.batch_size)
            
            # Compute similarities
            similarities = torch.nn.functional.cosine_similarity(
                embeddings1, embeddings2, dim=-1
            )
            
            custom_metrics["custom_similarity"] = {
                "mean_similarity": similarities.mean().item(),
                "std_similarity": similarities.std().item(),
                "min_similarity": similarities.min().item(),
                "max_similarity": similarities.max().item()
            }
            
            logger.info(f"Custom similarity - Mean: {similarities.mean().item():.4f}")
    
    # Example: Evaluate embedding quality
    if "embedding_analysis" in eval_config:
        logger.info("Running embedding analysis")
        
        analysis_config = eval_config["embedding_analysis"]
        sample_texts = analysis_config.get("sample_texts", [])
        
        if sample_texts:
            # Encode sample texts
            with torch.no_grad():
                embeddings = model.encode(sample_texts, batch_size=args.batch_size)
            
            # Analyze embeddings
            evaluator = ComprehensiveEvaluator(eval_config)
            quality_metrics = evaluator.evaluate_embedding_quality(embeddings)
            
            custom_metrics["embedding_quality"] = quality_metrics
            
            logger.info(f"Embedding analysis completed: {len(quality_metrics)} metrics")
    
    return custom_metrics


def save_embeddings(model, texts: list, output_path: Path, 
                   batch_size: int, device: torch.device):
    """Save embeddings for given texts."""
    logger.info(f"Computing and saving embeddings for {len(texts)} texts")
    
    # Encode texts
    with torch.no_grad():
        embeddings = model.encode(texts, batch_size=batch_size)
    
    # Save embeddings
    torch.save({
        'embeddings': embeddings.cpu(),
        'texts': texts,
        'embedding_dim': embeddings.size(-1),
        'num_texts': len(texts)
    }, output_path)
    
    logger.info(f"Saved embeddings to {output_path}")


def main():
    """Main evaluation function."""
    args = parse_args()
    
    # Setup output directory
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Setup logging
    log_file = output_dir / "evaluation.log"
    setup_logging(log_file, debug=args.debug)
    
    # Log arguments
    logger.info("Evaluation arguments:")
    for key, value in vars(args).items():
        logger.info(f"  {key}: {value}")
    
    # Setup device
    device = setup_device(args.device)
    
    # Load configurations
    config_manager = ConfigManager()
    model_config = config_manager.load_config(args.model_config)
    eval_config = config_manager.load_config(args.eval_config)
    
    try:
        # Load model
        model = load_model(args.model_path, model_config, device)
        
        # Enable half precision if requested
        if args.fp16 and device.type == "cuda":
            model = model.half()
            logger.info("Enabled half precision")
        
        # Run benchmark evaluations
        benchmark_results = run_benchmarks(model, eval_config, device, output_dir, args)
        
        # Run custom evaluations
        custom_results = run_custom_evaluation(model, eval_config, device, output_dir, args)
        
        # Save embeddings if requested
        if args.save_embeddings and "sample_texts" in eval_config:
            sample_texts = eval_config["sample_texts"]
            embeddings_path = output_dir / "sample_embeddings.pt"
            save_embeddings(model, sample_texts, embeddings_path, args.batch_size, device)
        
        # Combine and save all results
        all_results = {
            "benchmark_results": [
                {
                    "name": result.benchmark_name,
                    "metrics": result.metrics,
                    "num_samples": result.num_samples,
                    "description": result.description
                }
                for result in benchmark_results
            ],
            "custom_results": custom_results,
            "model_info": {
                "model_path": args.model_path,
                "embedding_dim": model.get_embedding_dim(),
                "num_parameters": model.get_num_parameters(),
                "device": str(device),
                "fp16": args.fp16
            }
        }
        
        # Save comprehensive results
        import json
        results_file = output_dir / "evaluation_results.json"
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(all_results, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Saved comprehensive results to {results_file}")
        
        # Print summary
        print("\n" + "="*60)
        print("EVALUATION SUMMARY")
        print("="*60)
        
        print(f"Model: {args.model_path}")
        print(f"Embedding Dimension: {model.get_embedding_dim()}")
        print(f"Parameters: {model.get_num_parameters():,}")
        print(f"Device: {device}")
        
        print(f"\nBenchmark Results: {len(benchmark_results)} benchmarks")
        for result in benchmark_results:
            print(f"  {result.benchmark_name}: {len(result.metrics)} metrics")
        
        if custom_results:
            print(f"\nCustom Results: {len(custom_results)} evaluations")
            for name, metrics in custom_results.items():
                print(f"  {name}: {len(metrics)} metrics")
        
        print(f"\nResults saved to: {output_dir}")
        print("="*60)
        
    except Exception as e:
        logger.error(f"Evaluation failed: {e}")
        raise
    
    logger.info("Evaluation completed successfully")


if __name__ == "__main__":
    main()
