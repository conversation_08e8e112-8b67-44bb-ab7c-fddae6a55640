#!/usr/bin/env python3
"""
Main training script for Persian text embedding distillation.
"""

import os
import sys
import argparse
import logging
import torch
import torch.multiprocessing as mp
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent.parent / "src"))

from utils.config import ConfigManager
from training.trainer import DistillationTrainer
from training.distributed import setup_environment_variables
from utils.logging import setup_logging
from utils.memory import setup_memory_optimization

logger = logging.getLogger(__name__)


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Train Persian text embedding distillation model")
    
    # Configuration files
    parser.add_argument("--model-config", type=str, required=True,
                       help="Path to model configuration file")
    parser.add_argument("--training-config", type=str, required=True,
                       help="Path to training configuration file")
    parser.add_argument("--data-config", type=str, required=True,
                       help="Path to data configuration file")
    
    # Override options
    parser.add_argument("--output-dir", type=str, default="outputs",
                       help="Output directory for checkpoints and logs")
    parser.add_argument("--resume-from", type=str, default=None,
                       help="Path to checkpoint to resume from")
    parser.add_argument("--seed", type=int, default=42,
                       help="Random seed")
    
    # Distributed training
    parser.add_argument("--world-size", type=int, default=None,
                       help="Number of processes for distributed training")
    parser.add_argument("--rank", type=int, default=None,
                       help="Process rank for distributed training")
    parser.add_argument("--local-rank", type=int, default=None,
                       help="Local rank for distributed training")
    parser.add_argument("--master-addr", type=str, default="localhost",
                       help="Master address for distributed training")
    parser.add_argument("--master-port", type=int, default=None,
                       help="Master port for distributed training")
    
    # Debug options
    parser.add_argument("--debug", action="store_true",
                       help="Enable debug mode")
    parser.add_argument("--profile", action="store_true",
                       help="Enable profiling")
    
    return parser.parse_args()


def setup_environment(args):
    """Setup training environment."""
    # Set random seed
    torch.manual_seed(args.seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed_all(args.seed)
    
    # Setup output directory
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Setup logging
    log_file = output_dir / "training.log"
    setup_logging(log_file, debug=args.debug)
    
    # Setup distributed training environment
    if args.world_size and args.world_size > 1:
        setup_environment_variables(args.master_addr, args.master_port)
    
    # Setup memory optimization
    setup_memory_optimization()
    
    logger.info(f"Environment setup complete. Output dir: {output_dir}")


def train_single_gpu(args):
    """Train on single GPU."""
    logger.info("Starting single GPU training")
    
    # Load configurations
    config_manager = ConfigManager()
    model_config = config_manager.load_config(args.model_config)
    training_config = config_manager.load_config(args.training_config)
    data_config = config_manager.load_config(args.data_config)
    
    # Override output directory
    training_config["logging"]["output_dir"] = args.output_dir
    
    # Initialize trainer
    trainer = DistillationTrainer(model_config, training_config, data_config)
    
    # Resume from checkpoint if specified
    if args.resume_from:
        trainer.load_checkpoint(args.resume_from)
        logger.info(f"Resumed training from {args.resume_from}")
    
    try:
        # Start training
        trainer.train()
        
    except KeyboardInterrupt:
        logger.info("Training interrupted by user")
        
    except Exception as e:
        logger.error(f"Training failed with error: {e}")
        raise
        
    finally:
        # Cleanup
        trainer.cleanup()
        logger.info("Training completed")


def train_worker(rank, world_size, args):
    """Worker function for distributed training."""
    logger.info(f"Starting training worker: rank {rank}/{world_size}")
    
    # Load configurations
    config_manager = ConfigManager()
    model_config = config_manager.load_config(args.model_config)
    training_config = config_manager.load_config(args.training_config)
    data_config = config_manager.load_config(args.data_config)
    
    # Override distributed settings
    training_config["distributed"]["world_size"] = world_size
    training_config["logging"]["output_dir"] = args.output_dir
    
    # Initialize trainer
    trainer = DistillationTrainer(model_config, training_config, data_config)
    
    # Setup distributed training
    trainer.setup_distributed(rank)
    
    # Resume from checkpoint if specified
    if args.resume_from:
        trainer.load_checkpoint(args.resume_from)
        logger.info(f"Resumed training from {args.resume_from}")
    
    try:
        # Start training
        trainer.train()
        
    except KeyboardInterrupt:
        logger.info("Training interrupted by user")
        
    except Exception as e:
        logger.error(f"Training failed with error: {e}")
        raise
        
    finally:
        # Cleanup
        trainer.cleanup()
        logger.info(f"Training worker {rank} completed")


def train_distributed(args):
    """Train with distributed setup."""
    world_size = args.world_size or torch.cuda.device_count()
    
    if world_size <= 1:
        logger.warning("World size <= 1, falling back to single GPU training")
        return train_single_gpu(args)
    
    logger.info(f"Starting distributed training with {world_size} processes")
    
    # Spawn training processes
    mp.spawn(
        train_worker,
        args=(world_size, args),
        nprocs=world_size,
        join=True
    )


def main():
    """Main function."""
    args = parse_args()
    
    # Setup environment
    setup_environment(args)
    
    # Log arguments
    logger.info("Training arguments:")
    for key, value in vars(args).items():
        logger.info(f"  {key}: {value}")
    
    # Check CUDA availability
    if not torch.cuda.is_available():
        logger.warning("CUDA not available, training will be slow")
    else:
        logger.info(f"CUDA available with {torch.cuda.device_count()} GPUs")
    
    # Start training
    try:
        if args.world_size and args.world_size > 1:
            train_distributed(args)
        else:
            train_single_gpu(args)
            
    except Exception as e:
        logger.error(f"Training failed: {e}")
        sys.exit(1)
    
    logger.info("Training script completed successfully")


if __name__ == "__main__":
    main()
